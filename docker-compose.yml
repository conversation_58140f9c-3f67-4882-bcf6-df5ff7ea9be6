# DevHQ Development Environment
# Complete Docker setup for backend, frontend, and database

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: devhq-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: devhq
      POSTGRES_USER: devhq_user
      POSTGRES_PASSWORD: devhq_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U devhq_user -d devhq"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and background jobs
  redis:
    image: redis:7-alpine
    container_name: devhq-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: devhq-backend
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: ****************************************************/devhq
      TEST_DATABASE_URL: ****************************************************/devhq_test
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      
      # Security
      SECRET_KEY: dev-secret-key-change-in-production
      ACCESS_TOKEN_EXPIRE_MINUTES: 15
      REFRESH_TOKEN_EXPIRE_DAYS: 7
      
      # External Services
      STRIPE_SECRET_KEY: sk_test_your_stripe_key_here
      STRIPE_WEBHOOK_SECRET: whsec_your_webhook_secret_here
      CLOUDINARY_URL: cloudinary://your_cloudinary_url_here
      
      # Email
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USER: <EMAIL>
      SMTP_PASSWORD: your-app-password
      
      # Environment
      ENVIRONMENT: development
      DEBUG: true
      
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: devhq-frontend
    restart: unless-stopped
    environment:
      # API Configuration
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_API_VERSION: v1
      
      # External Services
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: pk_test_your_stripe_key_here
      NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: your_cloudinary_name
      
      # Analytics
      NEXT_PUBLIC_VERCEL_ANALYTICS_ID: your_analytics_id
      NEXT_PUBLIC_SENTRY_DSN: your_sentry_dsn
      
      # Feature Flags
      NEXT_PUBLIC_ENABLE_PAYMENTS: true
      NEXT_PUBLIC_ENABLE_CLIENT_PORTAL: true
      
      # Environment
      NODE_ENV: development
      
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - devhq-network
    command: npm run dev

  # Celery Worker (for background jobs)
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: devhq-celery-worker
    restart: unless-stopped
    environment:
      DATABASE_URL: ****************************************************/devhq
      REDIS_URL: redis://redis:6379/0
      SECRET_KEY: dev-secret-key-change-in-production
      STRIPE_SECRET_KEY: sk_test_your_stripe_key_here
      CLOUDINARY_URL: cloudinary://your_cloudinary_url_here
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USER: <EMAIL>
      SMTP_PASSWORD: your-app-password
      ENVIRONMENT: development
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    command: celery -A app.tasks.celery_app worker --loglevel=info

  # Celery Beat (for scheduled tasks)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: devhq-celery-beat
    restart: unless-stopped
    environment:
      DATABASE_URL: ****************************************************/devhq
      REDIS_URL: redis://redis:6379/0
      SECRET_KEY: dev-secret-key-change-in-production
      ENVIRONMENT: development
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    command: celery -A app.tasks.celery_app beat --loglevel=info

  # Nginx (for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: devhq-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - devhq-network
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local

networks:
  devhq-network:
    driver: bridge