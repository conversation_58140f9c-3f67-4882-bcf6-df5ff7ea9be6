# 🐳 DevHQ Docker & CI/CD Setup Guide

Complete guide for setting up the DevHQ development environment with Docker and automated CI/CD pipeline.

## 🚀 Quick Start

### Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- Git 2.30+
- 8GB RAM minimum
- 20GB free disk space

### One-Command Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/devhq.git
cd devhq

# Run the automated setup script
./scripts/dev-setup.sh

# Start all services
docker-compose up -d
```

**That's it!** 🎉 Your development environment is ready!

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Database**: localhost:5432
- **Redis**: localhost:6379

---

## 📋 Services Overview

### Core Services

| Service      | Port | Description                     |
| ------------ | ---- | ------------------------------- |
| **frontend** | 3000 | Next.js cyberpunk dashboard     |
| **backend**  | 8000 | FastAPI server with auto-reload |
| **postgres** | 5432 | PostgreSQL 15 database          |
| **redis**    | 6379 | Redis for caching and jobs      |

### Background Services

| Service           | Description               |
| ----------------- | ------------------------- |
| **celery-worker** | Background job processing |
| **celery-beat**   | Scheduled task management |
| **nginx**         | Production reverse proxy  |

---

## 🛠️ Development Workflow

### Starting Services

```bash
# Start all services
docker-compose up -d

# Start specific services
docker-compose up -d postgres redis backend

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

### Development Commands

```bash
# Backend development
docker-compose exec backend bash
cd /app && python -m pytest tests/
cd /app && alembic upgrade head

# Frontend development
docker-compose exec frontend sh
npm run dev
npm run test
npm run build

# Database operations
docker-compose exec postgres psql -U devhq_user -d devhq
```

### Hot Reloading

Both backend and frontend support hot reloading:

- **Backend**: FastAPI auto-reloads on file changes
- **Frontend**: Next.js hot module replacement

---

## 🔧 Configuration

### Environment Variables

> **🔒 SECURITY WARNING**: Never commit `.env` files to version control! Always set secrets via GitHub/Vercel/Fly environment dashboards in production.

#### Backend (.env)

```bash
# Database
DATABASE_URL=****************************************************/devhq
TEST_DATABASE_URL=****************************************************/devhq_test

# Security - CHANGE THESE IN PRODUCTION!
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# External Services - USE YOUR ACTUAL KEYS
STRIPE_SECRET_KEY=sk_test_your_key_here
CLOUDINARY_URL=cloudinary://your_url_here
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

#### Frontend (.env.local)

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1

# External Services
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name

# Feature Flags
NEXT_PUBLIC_ENABLE_PAYMENTS=true
NEXT_PUBLIC_ENABLE_CLIENT_PORTAL=true
```

### Docker Compose Profiles

```bash
# Development (default)
docker-compose up -d

# Use development-specific compose file
docker-compose -f docker-compose.dev.yml up -d

# Use production-specific compose file
docker-compose -f docker-compose.prod.yml up -d

# Backend only
docker-compose up -d postgres redis backend celery-worker
```

### Volume Persistence & Data Management

#### Where Your Data Lives

```yaml
volumes:
  postgres_data:
    driver: local
    # Location: /var/lib/docker/volumes/devhq_postgres_data/_data
  redis_data:
    driver: local
    # Location: /var/lib/docker/volumes/devhq_redis_data/_data
```

#### Important Data Persistence Notes

- **Database data persists** even when containers are stopped
- **Data survives** `docker-compose down` (containers removed, volumes kept)
- **Data is lost** with `docker-compose down -v` (removes volumes)
- **For production**: Use managed databases (Fly.io Postgres, Neon, Supabase)

#### Backup Your Data

```bash
# Export database backup
docker-compose exec postgres pg_dump -U devhq_user devhq > backup.sql

# Import database backup
docker-compose exec -T postgres psql -U devhq_user devhq < backup.sql

# Export Redis data
docker-compose exec redis redis-cli BGSAVE
docker cp devhq-redis:/data/dump.rdb ./redis-backup.rdb
```

---

## 🧪 Testing

### Utility Scripts

DevHQ includes helpful scripts to streamline common development tasks:

```bash
scripts/
├── dev-setup.sh      # One-command environment setup
├── reset-db.sh       # Reset database with fresh migrations
├── run-tests.sh      # Comprehensive test runner
└── clear-containers.sh # Docker cleanup utility
```

#### Quick Test Commands

```bash
# Run comprehensive test suite
./scripts/run-tests.sh

# Run only backend tests with coverage
./scripts/run-tests.sh --backend-only --coverage

# Run only frontend tests
./scripts/run-tests.sh --frontend-only

# Run E2E tests
./scripts/run-tests.sh --e2e

# Get help
./scripts/run-tests.sh --help
```

### Manual Testing

```bash
# Backend tests
docker-compose exec backend pytest tests/ -v --cov=app

# Frontend tests
docker-compose exec frontend npm run test

# E2E tests
docker-compose exec frontend npm run test:e2e

# Load tests
docker-compose exec backend locust -f tests/load/locustfile.py
```

### Test Databases

- **Main DB**: `devhq`
- **Test DB**: `devhq_test` (automatically created)

### Database Management

```bash
# Reset database completely
./scripts/reset-db.sh

# Clear all containers and volumes
./scripts/clear-containers.sh

# Keep volumes but clean containers
./scripts/clear-containers.sh --keep-volumes

# Full Docker system cleanup
./scripts/clear-containers.sh --full
```

---

## 🚀 CI/CD Pipeline

### GitHub Actions Workflows

#### Main CI/CD Pipeline (`.github/workflows/ci-cd.yml`)

Triggers on push to `main` and `develop` branches:

1. **Backend Testing**
   - Code quality checks (Black, isort, mypy, bandit)
   - Unit and integration tests with coverage
   - Security vulnerability scanning

2. **Frontend Testing**
   - ESLint and TypeScript checks
   - Unit tests with coverage
   - E2E tests with Playwright
   - Build verification

3. **Security Scanning**
   - Trivy vulnerability scanner
   - Dependency audit (pip-audit, npm audit)
   - SARIF report upload to GitHub

4. **Docker Image Building**
   - Multi-stage builds for production
   - Images pushed to GitHub Container Registry
   - Automatic tagging and caching

5. **Deployment**
   - **Staging**: Auto-deploy `develop` branch
   - **Production**: Auto-deploy `main` branch
   - Smoke tests after deployment

#### Dependency Updates (`.github/workflows/dependency-update.yml`)

Automated weekly dependency updates:

- Python packages via pip-tools
- Node.js packages via npm-check-updates
- Automatic PR creation with changes

### Required Secrets

DevHQ's enhanced CI/CD pipeline requires several secrets for automated deployment, testing, and notifications.

**📋 Complete Setup Guide**: See [GITHUB_SECRETS_SETUP.md](./GITHUB_SECRETS_SETUP.md) for detailed instructions.

**Essential Secrets:**

```bash
# Deployment (Required)
FLY_API_TOKEN=your_fly_token
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id

# Notifications (Optional but recommended)
SLACK_WEBHOOK_URL=your_slack_webhook

# Testing (Optional - for comprehensive CI/CD)
PAYSTACK_SECRET_KEY_TEST=sk_test_your_key
PAYSTACK_PUBLIC_KEY_TEST=pk_test_your_key
CLOUDINARY_URL_TEST=cloudinary://your_test_url
```

**🔐 Security Note**: Never commit secrets to version control. Always use GitHub repository secrets or environment dashboards.

---

## 🏗️ Production Deployment

### Fly.io Backend Deployment

```bash
# Install Fly CLI
curl -L https://fly.io/install.sh | sh

# Login and create app
fly auth login
fly launch --name devhq-backend

# Deploy
fly deploy
```

### Vercel Frontend Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Login and deploy
vercel login
vercel --prod
```

### Environment Setup

1. **Database**: Use Fly.io Postgres or Neon
2. **Redis**: Use Fly.io Redis or Upstash
3. **File Storage**: Cloudinary
4. **Email**: Gmail SMTP or SendGrid
5. **Payments**: Stripe

---

## 🔍 Monitoring & Debugging

### Health Checks

```bash
# Service health
curl http://localhost:8000/health
curl http://localhost:3000/api/health

# Database connection
docker-compose exec postgres pg_isready -U devhq_user

# Redis connection
docker-compose exec redis redis-cli ping
```

### Logs

```bash
# View all logs
docker-compose logs -f

# Service-specific logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres

# Follow logs with timestamps
docker-compose logs -f -t backend
```

### Performance Monitoring

```bash
# Container stats
docker stats

# Service resource usage
docker-compose exec backend htop
docker-compose exec frontend htop
```

---

## 🛠️ Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Find process using port
lsof -i :3000
lsof -i :8000

# Kill process
kill -9 <PID>
```

#### Database Connection Issues

```bash
# Reset database
docker-compose down -v
docker-compose up -d postgres
sleep 10
docker-compose exec backend alembic upgrade head
```

#### Permission Issues

```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/dev-setup.sh
```

#### Out of Disk Space

```bash
# Clean Docker system
docker system prune -a --volumes

# Remove unused images
docker image prune -a
```

### Debug Mode

```bash
# Enable debug logging
export DEBUG=true
docker-compose up -d

# Access container shells
docker-compose exec backend bash
docker-compose exec frontend sh
docker-compose exec postgres psql -U devhq_user -d devhq
```

---

## 📚 Additional Resources

### Documentation

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)

### DevHQ Specific

- [Backend API Documentation](http://localhost:8000/docs)
- [Database Schema](./devhq_database_schema.dbml)
- [Frontend Design System](./frontend/design.md)
- [Development Tasks](./backend/tasks.md)

---

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and test locally
4. **Run the full test suite**: `./scripts/run-tests.sh`
5. **Commit your changes**: `git commit -m 'Add amazing feature'`
6. **Push to the branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Code Quality Standards

- **Backend**: Black formatting, isort imports, mypy type checking
- **Frontend**: ESLint rules, Prettier formatting, TypeScript strict mode
- **Tests**: Minimum 80% coverage required
- **Documentation**: Update relevant docs with changes

---

## 🎯 Next Steps

1. **Update environment variables** with your actual API keys
2. **Set up external services** (Stripe, Cloudinary, email)
3. **Configure GitHub secrets** for CI/CD
4. **Run the full test suite** to ensure everything works
5. **Start building** your revolutionary cyberpunk developer dashboard!

**Happy coding!** 🚀⚡🔥
