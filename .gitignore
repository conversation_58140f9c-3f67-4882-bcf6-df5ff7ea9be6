# DevHQ - Git Ignore Configuration
# Strategic file management: Keep infrastructure public, hide development docs

# =============================================================================
# DEVELOPMENT DOCUMENTATION (PRIVATE - COMPETITIVE ADVANTAGE)
# =============================================================================

# Backend Documentation (Private)
backend/tasks.md
backend/design.md
backend/requirements.md
DevHQ_Backend_Build_Plan.md
DevHQ_Payment_System_Design.md

# Frontend Documentation (Private)
frontend/tasks.md
frontend/design.md
frontend/requirements.md
DevHQ_Frontend_Build_Plan.md

# Database Schema (Private)
devhq_database_schema.dbml

# =============================================================================
# KEEP THESE INFRASTRUCTURE FILES PUBLIC
# =============================================================================
# These files are intentionally kept public for community and professional credibility:
# ✅ README.md (project showcase)
# ✅ LICENSE (open source license)
# ✅ DOCKER_SETUP.md (infrastructure documentation)
# ✅ docker-compose*.yml (development setup)
# ✅ backend/Dockerfile (containerization)
# ✅ frontend/Dockerfile (containerization)
# ✅ .github/workflows/ (CI/CD pipeline)
# ✅ nginx/ (reverse proxy config)
# ✅ scripts/ (utility scripts)

# =============================================================================
# RUNTIME ENVIRONMENT FILES (PRIVATE - CONTAINS SECRETS)
# =============================================================================

# Environment files with secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.example
backend/.env
frontend/.env.local

# Configuration files with secrets
config.py
settings.py
local_settings.py

# =============================================================================
# DEVELOPMENT ENVIRONMENTS
# =============================================================================

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# =============================================================================
# ENVIRONMENT & CONFIGURATION
# =============================================================================

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.example

# Configuration files
config.py
settings.py
local_settings.py

# =============================================================================
# DATABASE & DATA
# =============================================================================

# Database files
*.db
*.sqlite
*.sqlite3
db.sqlite3
database.db

# Database dumps
*.sql
*.dump

# Migration files (keep structure, hide data)
**/migrations/versions/
alembic/versions/

# =============================================================================
# LOGS & TEMPORARY FILES
# =============================================================================

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Temporary files
tmp/
temp/
.tmp/
.temp/

# =============================================================================
# IDE & EDITOR FILES
# =============================================================================

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# BUILD & DEPLOYMENT
# =============================================================================

# Next.js
.next/
out/
build/
dist/

# Nuxt.js
.nuxt/
.output/

# Gatsby
.cache/
public/

# Webpack
.webpack/

# Docker (Keep main compose files public, hide overrides)
Dockerfile.dev
docker-compose.override.yml

# =============================================================================
# TESTING & COVERAGE
# =============================================================================

# Test coverage
coverage/
.coverage
.coverage.*
htmlcov/
.tox/
.nox/
.pytest_cache/
cover/
*.cover
*.py,cover
.hypothesis/

# Jest
coverage/
.nyc_output/

# =============================================================================
# SECURITY & SECRETS
# =============================================================================

# API Keys and secrets
secrets/
.secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# OAuth tokens
.oauth/
tokens/

# =============================================================================
# CLOUD & DEPLOYMENT SPECIFIC
# =============================================================================

# Vercel
.vercel/

# Fly.io
fly.toml.backup

# AWS
.aws/

# Google Cloud
.gcloud/

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Sentry
.sentryclirc

# Analytics
.analytics/

# =============================================================================
# BACKUP & ARCHIVE FILES
# =============================================================================

# Backup files
*.bak
*.backup
*.old
*.orig
*.save

# Archive files
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z

# =============================================================================
# CUSTOM PROJECT EXCLUSIONS
# =============================================================================

# Development scripts
scripts/dev/
scripts/local/

# Test data
test-data/
sample-data/
fixtures/

# Documentation drafts
docs/drafts/
docs/internal/

# Design files
design/
mockups/
wireframes/

# Research and planning
research/
planning/
notes/

# =============================================================================
# KEEP THESE FILES PUBLIC
# =============================================================================

# These files should remain in the repository:
# README.md
# LICENSE

# =============================================================================
# FORCE INCLUDE IMPORTANT FILES (if needed)
# =============================================================================

# Uncomment to force include specific files that might be ignored by patterns above
# !important-config.example
# !.github/workflows/
# !docs/public/