# DevHQ Development Environment
# Optimized for local development with hot reloading and debugging

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: devhq-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: devhq
      POSTGRES_USER: devhq_user
      POSTGRES_PASSWORD: devhq_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U devhq_user -d devhq"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and background jobs
  redis:
    image: redis:7-alpine
    container_name: devhq-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: devhq-backend-dev
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: ****************************************************/devhq
      TEST_DATABASE_URL: ****************************************************/devhq_test
      
      # Redis
      REDIS_URL: redis://redis:6379/0
      
      # Security
      SECRET_KEY: dev-secret-key-change-in-production
      ACCESS_TOKEN_EXPIRE_MINUTES: 15
      REFRESH_TOKEN_EXPIRE_DAYS: 7
      
      # External Services (Development keys)
      STRIPE_SECRET_KEY: sk_test_your_stripe_key_here
      STRIPE_WEBHOOK_SECRET: whsec_your_webhook_secret_here
      CLOUDINARY_URL: cloudinary://your_cloudinary_url_here
      
      # Email (Development)
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USER: <EMAIL>
      SMTP_PASSWORD: your-app-password
      
      # Environment
      ENVIRONMENT: development
      DEBUG: true
      LOG_LEVEL: DEBUG
      
    ports:
      - "8000:8000"
    volumes:
      # Mount source code for hot reloading
      - ./backend:/app
      - backend_dev_logs:/app/logs
      # Exclude node_modules and __pycache__ for performance
      - /app/__pycache__
      - /app/.pytest_cache
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Development command with auto-reload
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug

  # Next.js Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: devhq-frontend-dev
    restart: unless-stopped
    environment:
      # API Configuration
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_API_VERSION: v1
      
      # External Services (Development keys)
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: pk_test_your_stripe_key_here
      NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: your_cloudinary_name
      
      # Analytics (Development)
      NEXT_PUBLIC_VERCEL_ANALYTICS_ID: your_analytics_id
      NEXT_PUBLIC_SENTRY_DSN: your_sentry_dsn
      
      # Feature Flags
      NEXT_PUBLIC_ENABLE_PAYMENTS: true
      NEXT_PUBLIC_ENABLE_CLIENT_PORTAL: true
      
      # Environment
      NODE_ENV: development
      
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reloading
      - ./frontend:/app
      # Exclude node_modules and .next for performance
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - devhq-network
    # Development command with hot reloading
    command: npm run dev

  # Celery Worker (Development)
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: devhq-celery-worker-dev
    restart: unless-stopped
    environment:
      DATABASE_URL: ****************************************************/devhq
      REDIS_URL: redis://redis:6379/0
      SECRET_KEY: dev-secret-key-change-in-production
      STRIPE_SECRET_KEY: sk_test_your_stripe_key_here
      CLOUDINARY_URL: cloudinary://your_cloudinary_url_here
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USER: <EMAIL>
      SMTP_PASSWORD: your-app-password
      ENVIRONMENT: development
      LOG_LEVEL: DEBUG
    volumes:
      - ./backend:/app
      - backend_dev_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    command: celery -A app.tasks.celery_app worker --loglevel=debug --reload

  # Celery Beat (Development)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: devhq-celery-beat-dev
    restart: unless-stopped
    environment:
      DATABASE_URL: ****************************************************/devhq
      REDIS_URL: redis://redis:6379/0
      SECRET_KEY: dev-secret-key-change-in-production
      ENVIRONMENT: development
      LOG_LEVEL: DEBUG
    volumes:
      - ./backend:/app
      - backend_dev_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    command: celery -A app.tasks.celery_app beat --loglevel=debug

volumes:
  postgres_dev_data:
    driver: local
    # Data persists in Docker's volume directory
    # Location: /var/lib/docker/volumes/devhq_postgres_dev_data/_data
  redis_dev_data:
    driver: local
  backend_dev_logs:
    driver: local

networks:
  devhq-network:
    driver: bridge