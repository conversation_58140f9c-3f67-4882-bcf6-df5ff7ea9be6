#!/bin/bash

# DevHQ Database Reset Script
# Completely resets the database and runs fresh migrations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning "⚠️  This will completely reset your database and delete all data!"
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Database reset cancelled."
    exit 0
fi

print_status "🗄️  Resetting DevHQ database..."

# Stop services that depend on the database
print_status "Stopping dependent services..."
docker-compose stop backend celery-worker celery-beat

# Stop and remove database container with volumes
print_status "Removing database container and volumes..."
docker-compose stop postgres
docker-compose rm -f postgres
docker volume rm devhq_postgres_data 2>/dev/null || true
docker volume rm devhq_postgres_dev_data 2>/dev/null || true

# Start fresh database
print_status "Starting fresh database..."
docker-compose up -d postgres

# Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 15

# Check if database is ready
until docker-compose exec postgres pg_isready -U devhq_user -d devhq; do
    print_status "Waiting for database connection..."
    sleep 2
done

print_success "Database is ready!"

# Run migrations
print_status "Running database migrations..."
docker-compose exec backend alembic upgrade head

# Optionally seed with test data
read -p "Do you want to seed the database with test data? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Seeding database with test data..."
    docker-compose exec backend python scripts/seed_data.py
    print_success "Test data seeded!"
fi

# Start all services
print_status "Starting all services..."
docker-compose up -d

print_success "🎉 Database reset complete!"
print_status "You can now access:"
print_status "- Frontend: http://localhost:3000"
print_status "- Backend API: http://localhost:8000"
print_status "- API Docs: http://localhost:8000/docs"