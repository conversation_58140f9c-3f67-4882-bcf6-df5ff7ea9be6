#!/usr/bin/env python3
"""
DevHQ Test Data Seeding Script
Seeds the test database with realistic data for comprehensive testing
"""

import os
import sys
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from uuid import uuid4

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.user import User, UserSettings, UserSession
from app.models.client import Client
from app.models.project import Project, ProjectMilestone
from app.models.task import Task, TimeEntry
from app.models.wallet import WalletAccount, Transaction
from app.models.invoice import Invoice, InvoiceItem
from app.models.approval import ClientApproval, ClientFeedback
from app.models.note import ProjectNote
from app.models.upload import DesignUpload
from app.core.security import get_password_hash
from app.database import Base

# Test data constants
TEST_USERS = [
    {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "Developer"
    },
    {
        "email": "<EMAIL>", 
        "password": "DemoPassword123!",
        "first_name": "Demo",
        "last_name": "User"
    }
]

TEST_CLIENTS = [
    {
        "name": "Acme Corporation",
        "email": "<EMAIL>",
        "company": "Acme Corp",
        "lead_status": "active",
        "lead_source": "referral",
        "estimated_project_value": Decimal("15000.00")
    },
    {
        "name": "TechStart Inc",
        "email": "<EMAIL>",
        "company": "TechStart Inc",
        "lead_status": "prospect",
        "lead_source": "website",
        "estimated_project_value": Decimal("8500.00")
    },
    {
        "name": "Global Solutions",
        "email": "<EMAIL>",
        "company": "Global Solutions Ltd",
        "lead_status": "active",
        "lead_source": "linkedin",
        "estimated_project_value": Decimal("25000.00")
    }
]

TEST_PROJECTS = [
    {
        "name": "E-commerce Platform",
        "description": "Modern e-commerce platform with React and Node.js",
        "status": "active",
        "priority": "high",
        "budget": Decimal("15000.00"),
        "hourly_rate": Decimal("75.00"),
        "completion_percentage": Decimal("65.50")
    },
    {
        "name": "Mobile App Development",
        "description": "Cross-platform mobile app using React Native",
        "status": "active", 
        "priority": "medium",
        "budget": Decimal("12000.00"),
        "hourly_rate": Decimal("80.00"),
        "completion_percentage": Decimal("30.00")
    },
    {
        "name": "Website Redesign",
        "description": "Complete website redesign with modern UI/UX",
        "status": "completed",
        "priority": "medium",
        "budget": Decimal("8500.00"),
        "hourly_rate": Decimal("70.00"),
        "completion_percentage": Decimal("100.00")
    }
]

def create_test_database():
    """Create all database tables"""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is required")
    
    engine = create_engine(database_url)
    Base.metadata.create_all(bind=engine)
    return engine

def seed_database():
    """Seed the database with test data"""
    print("🌱 Starting database seeding...")
    
    # Create database engine and session
    engine = create_test_database()
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Clear existing data
        print("🧹 Clearing existing test data...")
        db.query(ClientFeedback).delete()
        db.query(ClientApproval).delete()
        db.query(InvoiceItem).delete()
        db.query(Invoice).delete()
        db.query(TimeEntry).delete()
        db.query(Task).delete()
        db.query(ProjectNote).delete()
        db.query(DesignUpload).delete()
        db.query(ProjectMilestone).delete()
        db.query(Transaction).delete()
        db.query(WalletAccount).delete()
        db.query(Project).delete()
        db.query(Client).delete()
        db.query(UserSession).delete()
        db.query(UserSettings).delete()
        db.query(User).delete()
        db.commit()
        
        # Create test users
        print("👤 Creating test users...")
        users = []
        for user_data in TEST_USERS:
            user = User(
                id=uuid4(),
                email=user_data["email"],
                password_hash=get_password_hash(user_data["password"]),
                first_name=user_data["first_name"],
                last_name=user_data["last_name"],
                is_active=True
            )
            db.add(user)
            users.append(user)
            
            # Create user settings
            settings = UserSettings(
                id=uuid4(),
                user_id=user.id,
                theme="dark",
                default_currency="USD",
                timezone="UTC",
                invoice_prefix="INV",
                default_hourly_rate=Decimal("75.00")
            )
            db.add(settings)
        
        db.commit()
        print(f"✅ Created {len(users)} test users")
        
        # Create test clients for first user
        print("🏢 Creating test clients...")
        clients = []
        for client_data in TEST_CLIENTS:
            client = Client(
                id=uuid4(),
                user_id=users[0].id,
                name=client_data["name"],
                email=client_data["email"],
                company=client_data["company"],
                lead_status=client_data["lead_status"],
                lead_source=client_data["lead_source"],
                estimated_project_value=client_data["estimated_project_value"],
                portal_enabled=True,
                portal_access_token=str(uuid4())
            )
            db.add(client)
            clients.append(client)
        
        db.commit()
        print(f"✅ Created {len(clients)} test clients")
        
        # Create wallet accounts
        print("💰 Creating wallet accounts...")
        wallet_accounts = []
        account_types = [
            ("Business Checking", "checking", Decimal("15420.50")),
            ("Savings Account", "savings", Decimal("25000.00")),
            ("Emergency Fund", "savings", Decimal("10000.00"))
        ]
        
        for name, account_type, balance in account_types:
            account = WalletAccount(
                id=uuid4(),
                user_id=users[0].id,
                name=name,
                account_type=account_type,
                balance=balance,
                currency="USD"
            )
            db.add(account)
            wallet_accounts.append(account)
        
        db.commit()
        print(f"✅ Created {len(wallet_accounts)} wallet accounts")
        
        # Create test projects
        print("🎯 Creating test projects...")
        projects = []
        for i, project_data in enumerate(TEST_PROJECTS):
            project = Project(
                id=uuid4(),
                user_id=users[0].id,
                client_id=clients[i % len(clients)].id,
                name=project_data["name"],
                description=project_data["description"],
                status=project_data["status"],
                priority=project_data["priority"],
                budget=project_data["budget"],
                hourly_rate=project_data["hourly_rate"],
                completion_percentage=project_data["completion_percentage"],
                start_date=datetime.now().date() - timedelta(days=30),
                end_date=datetime.now().date() + timedelta(days=60),
                color="#3B82F6"
            )
            db.add(project)
            projects.append(project)
        
        db.commit()
        print(f"✅ Created {len(projects)} test projects")
        
        # Create project milestones
        print("🎯 Creating project milestones...")
        milestones = []
        milestone_data = [
            ("Project Setup & Planning", 25.0, 2500.00),
            ("Core Development Phase", 50.0, 5000.00),
            ("Testing & QA", 75.0, 2500.00),
            ("Deployment & Launch", 100.0, 2500.00)
        ]
        
        for project in projects[:2]:  # Only for active projects
            for i, (title, percentage, payment) in enumerate(milestone_data):
                milestone = ProjectMilestone(
                    id=uuid4(),
                    user_id=users[0].id,
                    project_id=project.id,
                    title=title,
                    target_date=datetime.now().date() + timedelta(days=15 * (i + 1)),
                    completion_percentage=Decimal(str(percentage)),
                    payment_amount=Decimal(str(payment)),
                    status="completed" if percentage <= project.completion_percentage else "pending",
                    is_client_visible=True
                )
                db.add(milestone)
                milestones.append(milestone)
        
        db.commit()
        print(f"✅ Created {len(milestones)} project milestones")
        
        # Create test tasks
        print("✅ Creating test tasks...")
        tasks = []
        task_data = [
            ("Set up development environment", "completed", "high"),
            ("Design database schema", "completed", "high"),
            ("Implement user authentication", "in_progress", "high"),
            ("Create API endpoints", "todo", "medium"),
            ("Frontend component development", "todo", "medium"),
            ("Write unit tests", "todo", "low")
        ]
        
        for project in projects:
            for title, status, priority in task_data:
                task = Task(
                    id=uuid4(),
                    user_id=users[0].id,
                    project_id=project.id,
                    title=f"{title} - {project.name}",
                    status=status,
                    priority=priority,
                    estimated_hours=Decimal("8.0"),
                    actual_hours=Decimal("6.5") if status == "completed" else Decimal("0.0"),
                    is_billable=True,
                    due_date=datetime.now() + timedelta(days=7)
                )
                db.add(task)
                tasks.append(task)
        
        db.commit()
        print(f"✅ Created {len(tasks)} test tasks")
        
        # Create time entries for completed tasks
        print("⏱️ Creating time entries...")
        time_entries = []
        completed_tasks = [t for t in tasks if t.status == "completed"]
        
        for task in completed_tasks[:5]:  # Limit to first 5 completed tasks
            # Create multiple time entries per task
            for day in range(3):
                entry = TimeEntry(
                    id=uuid4(),
                    user_id=users[0].id,
                    task_id=task.id,
                    project_id=task.project_id,
                    description=f"Working on {task.title}",
                    start_time=datetime.now() - timedelta(days=day, hours=2),
                    end_time=datetime.now() - timedelta(days=day),
                    duration_minutes=120,
                    is_billable=True,
                    hourly_rate=Decimal("75.00"),
                    billable_amount=Decimal("150.00")
                )
                db.add(entry)
                time_entries.append(entry)
        
        db.commit()
        print(f"✅ Created {len(time_entries)} time entries")
        
        # Create transactions
        print("💳 Creating financial transactions...")
        transactions = []
        transaction_data = [
            ("income", "Client Payment", 5000.00, "Client payment for milestone completion"),
            ("expense", "Software License", -299.00, "Annual IDE license"),
            ("expense", "Cloud Hosting", -89.99, "Monthly server costs"),
            ("income", "Consulting Fee", 1500.00, "Technical consulting session"),
            ("expense", "Office Supplies", -156.78, "Desk setup and equipment")
        ]
        
        for trans_type, category, amount, description in transaction_data:
            transaction = Transaction(
                id=uuid4(),
                user_id=users[0].id,
                wallet_account_id=wallet_accounts[0].id,
                project_id=projects[0].id if trans_type == "income" else None,
                client_id=clients[0].id if trans_type == "income" else None,
                type=trans_type,
                category=category,
                amount=Decimal(str(amount)),
                description=description,
                transaction_date=datetime.now() - timedelta(days=abs(int(amount)) % 30),
                is_tax_deductible=trans_type == "expense",
                tax_category="business_expense" if trans_type == "expense" else None
            )
            db.add(transaction)
            transactions.append(transaction)
        
        db.commit()
        print(f"✅ Created {len(transactions)} transactions")
        
        # Create test invoices
        print("🧾 Creating test invoices...")
        invoices = []
        for i, project in enumerate(projects[:2]):
            invoice = Invoice(
                id=uuid4(),
                user_id=users[0].id,
                client_id=project.client_id,
                project_id=project.id,
                invoice_number=f"INV-{1000 + i}",
                status="paid" if i == 0 else "sent",
                issue_date=datetime.now().date() - timedelta(days=15),
                due_date=datetime.now().date() + timedelta(days=15),
                subtotal=Decimal("2500.00"),
                tax_rate=Decimal("8.25"),
                tax_amount=Decimal("206.25"),
                total_amount=Decimal("2706.25"),
                paid_amount=Decimal("2706.25") if i == 0 else Decimal("0.00"),
                currency="USD",
                payment_method="devhq_payments",
                payment_link_token=str(uuid4())
            )
            db.add(invoice)
            invoices.append(invoice)
            
            # Create invoice items
            invoice_item = InvoiceItem(
                id=uuid4(),
                invoice_id=invoice.id,
                description="Development work - Milestone completion",
                quantity=Decimal("1.00"),
                unit_price=Decimal("2500.00"),
                total_price=Decimal("2500.00")
            )
            db.add(invoice_item)
        
        db.commit()
        print(f"✅ Created {len(invoices)} test invoices")
        
        # Create project notes
        print("📝 Creating project notes...")
        notes = []
        for project in projects:
            note = ProjectNote(
                id=uuid4(),
                user_id=users[0].id,
                project_id=project.id,
                title="Project Requirements",
                content=f"# {project.name} Requirements\n\n## Overview\n{project.description}\n\n## Key Features\n- Feature 1\n- Feature 2\n- Feature 3\n\n## Technical Notes\n- Use modern tech stack\n- Ensure mobile responsiveness\n- Implement proper testing",
                is_pinned=True
            )
            db.add(note)
            notes.append(note)
        
        db.commit()
        print(f"✅ Created {len(notes)} project notes")
        
        print("🎉 Database seeding completed successfully!")
        print(f"""
📊 Test Data Summary:
- Users: {len(users)}
- Clients: {len(clients)}
- Projects: {len(projects)}
- Milestones: {len(milestones)}
- Tasks: {len(tasks)}
- Time Entries: {len(time_entries)}
- Transactions: {len(transactions)}
- Invoices: {len(invoices)}
- Notes: {len(notes)}

🔑 Test Login Credentials:
- Email: <EMAIL>
- Password: TestPassword123!

- Email: <EMAIL>  
- Password: DemoPassword123!
        """)
        
    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    seed_database()