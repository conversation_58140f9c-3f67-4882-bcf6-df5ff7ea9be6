#!/bin/bash

# DevHQ Container Cleanup Script
# Cleans up Docker containers, images, and volumes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
FULL_CLEANUP=false
KEEP_VOLUMES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --full)
            FULL_CLEANUP=true
            shift
            ;;
        --keep-volumes)
            KEEP_VOLUMES=true
            shift
            ;;
        -h|--help)
            echo "DevHQ Container Cleanup Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --full           Full cleanup including all Docker resources"
            echo "  --keep-volumes   Keep volumes (preserve database data)"
            echo "  -h, --help       Show this help message"
            echo ""
            echo "Default: Stops containers and removes unused resources"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "🧹 Cleaning up DevHQ Docker resources..."

# Stop all DevHQ services
print_status "Stopping DevHQ services..."
docker-compose down

# Remove DevHQ containers
print_status "Removing DevHQ containers..."
docker-compose rm -f

if [[ "$KEEP_VOLUMES" != true ]]; then
    print_warning "⚠️  This will remove all volumes and delete database data!"
    read -p "Are you sure you want to remove volumes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Removing DevHQ volumes..."
        docker-compose down -v
        
        # Remove named volumes
        docker volume rm devhq_postgres_data 2>/dev/null || true
        docker volume rm devhq_postgres_dev_data 2>/dev/null || true
        docker volume rm devhq_postgres_prod_data 2>/dev/null || true
        docker volume rm devhq_redis_data 2>/dev/null || true
        docker volume rm devhq_redis_dev_data 2>/dev/null || true
        docker volume rm devhq_redis_prod_data 2>/dev/null || true
        docker volume rm devhq_backend_logs 2>/dev/null || true
        docker volume rm devhq_backend_dev_logs 2>/dev/null || true
        docker volume rm devhq_backend_prod_logs 2>/dev/null || true
        docker volume rm devhq_nginx_logs 2>/dev/null || true
        
        print_success "Volumes removed!"
    else
        print_status "Keeping volumes..."
    fi
fi

# Remove DevHQ images
print_status "Removing DevHQ images..."
docker images | grep devhq | awk '{print $3}' | xargs -r docker rmi -f

if [[ "$FULL_CLEANUP" == true ]]; then
    print_warning "⚠️  Full cleanup will remove ALL unused Docker resources!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Performing full Docker cleanup..."
        
        # Remove all stopped containers
        print_status "Removing stopped containers..."
        docker container prune -f
        
        # Remove unused images
        print_status "Removing unused images..."
        docker image prune -a -f
        
        # Remove unused networks
        print_status "Removing unused networks..."
        docker network prune -f
        
        # Remove unused volumes (if not keeping them)
        if [[ "$KEEP_VOLUMES" != true ]]; then
            print_status "Removing unused volumes..."
            docker volume prune -f
        fi
        
        # Remove build cache
        print_status "Removing build cache..."
        docker builder prune -a -f
        
        print_success "Full cleanup completed!"
    else
        print_status "Skipping full cleanup..."
    fi
fi

# Show disk space saved
print_status "Checking disk space..."
df -h | grep -E "(Filesystem|/dev/)"

# Show remaining Docker resources
print_status ""
print_status "📊 Remaining Docker resources:"
echo "Containers:"
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Size}}"
echo ""
echo "Images:"
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
echo ""
echo "Volumes:"
docker volume ls
echo ""
echo "Networks:"
docker network ls

print_success "🎉 Cleanup completed!"
print_status ""
print_status "To restart DevHQ:"
print_status "  docker-compose up -d"
print_status ""
print_status "To rebuild from scratch:"
print_status "  docker-compose build --no-cache"
print_status "  docker-compose up -d"