#!/bin/bash

# DevHQ Development Environment Setup Script
# Automates the setup of the development environment

set -e

echo "🚀 Setting up DevHQ Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Git is installed
if ! command -v git &> /dev/null; then
    print_error "Git is not installed. Please install Git first."
    exit 1
fi

print_status "Checking system requirements..."
print_success "All system requirements met!"

# Create necessary directories
print_status "Creating project directories..."
mkdir -p backend/logs
mkdir -p frontend/.next
mkdir -p nginx/ssl
mkdir -p data/postgres
mkdir -p data/redis

# Copy environment files if they don't exist
print_status "Setting up environment files..."

if [ ! -f backend/.env ]; then
    print_status "Creating backend .env file..."
    cat > backend/.env << EOF
# Database
DATABASE_URL=postgresql://devhq_user:devhq_password@localhost:5432/devhq
TEST_DATABASE_URL=postgresql://devhq_user:devhq_password@localhost:5432/devhq_test

# Redis
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=dev-secret-key-change-in-production-$(openssl rand -hex 32)
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# External Services (replace with your keys)
STRIPE_SECRET_KEY=sk_test_your_stripe_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
CLOUDINARY_URL=cloudinary://your_cloudinary_url_here

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Environment
ENVIRONMENT=development
DEBUG=true
EOF
    print_success "Backend .env file created!"
else
    print_warning "Backend .env file already exists, skipping..."
fi

if [ ! -f frontend/.env.local ]; then
    print_status "Creating frontend .env.local file..."
    cat > frontend/.env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1

# External Services (replace with your keys)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_name

# Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_analytics_id
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# Feature Flags
NEXT_PUBLIC_ENABLE_PAYMENTS=true
NEXT_PUBLIC_ENABLE_CLIENT_PORTAL=true

# Environment
NODE_ENV=development
EOF
    print_success "Frontend .env.local file created!"
else
    print_warning "Frontend .env.local file already exists, skipping..."
fi

# Set up Git hooks
print_status "Setting up Git hooks..."
if [ -d .git ]; then
    # Create pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# DevHQ Pre-commit Hook

echo "Running pre-commit checks..."

# Check if backend code exists and run checks
if [ -d "backend" ]; then
    echo "Checking backend code..."
    cd backend
    
    # Run black formatting check
    if command -v black &> /dev/null; then
        black --check . || {
            echo "❌ Backend code formatting issues found. Run 'black .' to fix."
            exit 1
        }
    fi
    
    # Run isort check
    if command -v isort &> /dev/null; then
        isort --check-only . || {
            echo "❌ Backend import sorting issues found. Run 'isort .' to fix."
            exit 1
        }
    fi
    
    cd ..
fi

# Check if frontend code exists and run checks
if [ -d "frontend" ]; then
    echo "Checking frontend code..."
    cd frontend
    
    # Run ESLint
    if [ -f "package.json" ] && command -v npm &> /dev/null; then
        npm run lint || {
            echo "❌ Frontend linting issues found. Run 'npm run lint:fix' to fix."
            exit 1
        }
    fi
    
    cd ..
fi

echo "✅ Pre-commit checks passed!"
EOF

    chmod +x .git/hooks/pre-commit
    print_success "Git hooks set up!"
else
    print_warning "Not a Git repository, skipping Git hooks setup..."
fi

# Build and start services
print_status "Building Docker images..."
docker-compose build

print_status "Starting services..."
docker-compose up -d postgres redis

# Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 10

# Run database migrations (when backend is ready)
print_status "Database setup complete!"

print_success "🎉 DevHQ development environment setup complete!"
print_status ""
print_status "Next steps:"
print_status "1. Update the .env files with your actual API keys"
print_status "2. Run 'docker-compose up' to start all services"
print_status "3. Visit http://localhost:3000 for the frontend"
print_status "4. Visit http://localhost:8000/docs for the API documentation"
print_status ""
print_status "Useful commands:"
print_status "- docker-compose up -d          # Start all services in background"
print_status "- docker-compose logs -f        # View logs"
print_status "- docker-compose down           # Stop all services"
print_status "- docker-compose exec backend bash  # Access backend container"
print_status "- docker-compose exec frontend sh   # Access frontend container"