#!/bin/bash

# DevHQ Test Runner Script
# Runs comprehensive test suite for both backend and frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
BACKEND_ONLY=false
FRONTEND_ONLY=false
COVERAGE=false
E2E=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-only)
            BACKEND_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        --e2e)
            E2E=true
            shift
            ;;
        -h|--help)
            echo "DevHQ Test Runner"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --backend-only    Run only backend tests"
            echo "  --frontend-only   Run only frontend tests"
            echo "  --coverage        Generate coverage reports"
            echo "  --e2e            Run end-to-end tests"
            echo "  -h, --help       Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "🧪 Running DevHQ Test Suite..."

# Ensure services are running
print_status "Ensuring test services are running..."
docker-compose up -d postgres redis

# Wait for services to be ready
sleep 5

# Backend Tests
if [[ "$FRONTEND_ONLY" != true ]]; then
    print_status "🐍 Running Backend Tests..."
    
    # Code quality checks
    print_status "Running code quality checks..."
    docker-compose exec backend black --check . || {
        print_error "Code formatting issues found. Run 'black .' to fix."
        exit 1
    }
    
    docker-compose exec backend isort --check-only . || {
        print_error "Import sorting issues found. Run 'isort .' to fix."
        exit 1
    }
    
    docker-compose exec backend mypy app/ || {
        print_error "Type checking failed."
        exit 1
    }
    
    # Security audit
    print_status "Running security audit..."
    docker-compose exec backend bandit -r app/ -f json -o bandit-report.json || {
        print_warning "Security issues found. Check bandit-report.json"
    }
    
    # Unit and integration tests
    print_status "Running unit and integration tests..."
    if [[ "$COVERAGE" == true ]]; then
        docker-compose exec backend pytest tests/ -v --cov=app --cov-report=html --cov-report=xml
        print_success "Coverage report generated in htmlcov/"
    else
        docker-compose exec backend pytest tests/ -v
    fi
    
    print_success "✅ Backend tests completed!"
fi

# Frontend Tests
if [[ "$BACKEND_ONLY" != true ]]; then
    print_status "⚛️  Running Frontend Tests..."
    
    # Ensure frontend dependencies are installed
    docker-compose exec frontend npm ci
    
    # Linting
    print_status "Running ESLint..."
    docker-compose exec frontend npm run lint || {
        print_error "Linting issues found. Run 'npm run lint:fix' to fix."
        exit 1
    }
    
    # Type checking
    print_status "Running TypeScript checks..."
    docker-compose exec frontend npm run type-check || {
        print_error "TypeScript errors found."
        exit 1
    }
    
    # Unit tests
    print_status "Running unit tests..."
    if [[ "$COVERAGE" == true ]]; then
        docker-compose exec frontend npm run test:coverage
        print_success "Coverage report generated in coverage/"
    else
        docker-compose exec frontend npm run test
    fi
    
    # Build test
    print_status "Testing production build..."
    docker-compose exec frontend npm run build || {
        print_error "Build failed."
        exit 1
    }
    
    # E2E tests
    if [[ "$E2E" == true ]]; then
        print_status "Running E2E tests..."
        docker-compose exec frontend npx playwright install --with-deps
        docker-compose exec frontend npm run test:e2e || {
            print_error "E2E tests failed."
            exit 1
        }
        print_success "E2E tests completed!"
    fi
    
    print_success "✅ Frontend tests completed!"
fi

# Integration tests (if both backend and frontend are being tested)
if [[ "$BACKEND_ONLY" != true && "$FRONTEND_ONLY" != true ]]; then
    print_status "🔗 Running Integration Tests..."
    
    # Wait for all services to be ready
    sleep 10
    
    # Health checks
    print_status "Running health checks..."
    curl -f http://localhost:8000/health || {
        print_error "Backend health check failed."
        exit 1
    }
    
    curl -f http://localhost:3000/api/health || {
        print_error "Frontend health check failed."
        exit 1
    }
    
    print_success "✅ Integration tests completed!"
fi

print_success "🎉 All tests passed!"

# Generate summary
print_status ""
print_status "📊 Test Summary:"
if [[ "$BACKEND_ONLY" != true ]]; then
    print_status "✅ Frontend: Linting, TypeScript, Unit Tests, Build"
    if [[ "$E2E" == true ]]; then
        print_status "✅ Frontend: E2E Tests"
    fi
fi
if [[ "$FRONTEND_ONLY" != true ]]; then
    print_status "✅ Backend: Code Quality, Security, Unit Tests"
fi
if [[ "$BACKEND_ONLY" != true && "$FRONTEND_ONLY" != true ]]; then
    print_status "✅ Integration: Health Checks"
fi
if [[ "$COVERAGE" == true ]]; then
    print_status "📈 Coverage reports generated"
fi

print_status ""
print_status "Next steps:"
print_status "- Review any warnings or coverage reports"
print_status "- Fix any issues found"
print_status "- Commit your changes"