# Automated Dependency Updates
# Keep dependencies up to date with automated PRs

name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  # Update Python dependencies
  update-python-deps:
    name: Update Python Dependencies
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install pip-tools
      run: pip install pip-tools

    - name: Update requirements
      working-directory: ./backend
      run: |
        # Update requirements.in if it exists, otherwise use requirements.txt
        if [ -f requirements.in ]; then
          pip-compile --upgrade requirements.in
        fi
        
        if [ -f requirements-dev.in ]; then
          pip-compile --upgrade requirements-dev.in
        fi

    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update Python dependencies'
        title: '🔄 Update Python Dependencies'
        body: |
          Automated dependency update for Python packages.
          
          Please review the changes and ensure all tests pass before merging.
        branch: update-python-dependencies
        delete-branch: true

  # Update Node.js dependencies
  update-node-deps:
    name: Update Node.js Dependencies
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Update dependencies
      working-directory: ./frontend
      run: |
        # Update package.json
        npx npm-check-updates -u
        
        # Install updated dependencies
        npm install
        
        # Run audit fix
        npm audit fix

    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update Node.js dependencies'
        title: '🔄 Update Node.js Dependencies'
        body: |
          Automated dependency update for Node.js packages.
          
          Please review the changes and ensure all tests pass before merging.
        branch: update-node-dependencies
        delete-branch: true