# DevHQ CI/CD Pipeline
# Comprehensive GitHub Actions workflow for testing, building, and deployment

name: DevHQ CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Backend Testing and Quality Checks
  backend-test:
    name: Backend Tests & Quality
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: devhq_test_${{ github.run_id }}  # Isolated test DB per run
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt

    - name: Run linting
      working-directory: ./backend
      run: |
        # Code formatting check
        black --check .
        
        # Import sorting check
        isort --check-only .
        
        # Type checking
        mypy app/
        
        # Security audit
        bandit -r app/ -f json -o bandit-report.json || true

    - name: Run database migrations
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/devhq_test_${{ github.run_id }}
        REDIS_URL: redis://localhost:6379/0
        SECRET_KEY: test-secret-key-${{ github.run_id }}
        ENVIRONMENT: testing
      run: |
        # Run Alembic migrations to ensure DB is up to date
        alembic upgrade head

    - name: Seed test database
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/devhq_test_${{ github.run_id }}
        REDIS_URL: redis://localhost:6379/0
        SECRET_KEY: test-secret-key-${{ github.run_id }}
        ENVIRONMENT: testing
      run: |
        # Seed database with test data for comprehensive testing
        python scripts/seed_test_data.py

    - name: Run tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/devhq_test_${{ github.run_id }}
        REDIS_URL: redis://localhost:6379/0
        SECRET_KEY: test-secret-key-${{ github.run_id }}
        ENVIRONMENT: testing
      run: |
        # Run tests with coverage
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=html
        
        # Generate coverage badge
        coverage-badge -o coverage.svg

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

    - name: Upload test artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: backend-test-results
        retention-days: 3
        path: |
          backend/htmlcov/
          backend/bandit-report.json
          backend/coverage.svg

    - name: Notify backend test failure
      uses: 8398a7/action-slack@v3
      if: failure()
      with:
        status: failure
        text: '❌ DevHQ Backend Tests Failed - ${{ github.ref }}'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Frontend Testing and Quality Checks
  frontend-test:
    name: Frontend Tests & Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run linting
      working-directory: ./frontend
      run: |
        # ESLint check
        npm run lint
        
        # Type checking
        npm run type-check
        
        # Format checking
        npm run format:check

    - name: Run tests
      working-directory: ./frontend
      run: |
        # Unit tests
        npm run test:coverage
        
        # Build test
        npm run build

    - name: Run E2E tests
      working-directory: ./frontend
      run: |
        # Install Playwright browsers
        npx playwright install --with-deps
        
        # Run E2E tests
        npm run test:e2e

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

    - name: Upload test artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: frontend-test-results
        retention-days: 3
        path: |
          frontend/coverage/
          frontend/playwright-report/
          frontend/test-results/

    - name: Notify frontend test failure
      uses: 8398a7/action-slack@v3
      if: failure()
      with:
        status: failure
        text: '❌ DevHQ Frontend Tests Failed - ${{ github.ref }}'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Dependency vulnerability scan
      run: |
        # Backend dependency scan
        cd backend && pip-audit --format=json --output=pip-audit.json || true
        
        # Frontend dependency scan
        cd frontend && npm audit --audit-level=high --json > npm-audit.json || true

    - name: Upload security artifacts
      uses: actions/upload-artifact@v3
      with:
        name: security-scan-results
        retention-days: 7
        path: |
          trivy-results.sarif
          backend/pip-audit.json
          frontend/npm-audit.json

    - name: Notify security scan failure
      uses: 8398a7/action-slack@v3
      if: failure()
      with:
        status: failure
        text: '🔒 DevHQ Security Scan Failed - ${{ github.ref }}'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Build Docker Images
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test, security-scan]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    strategy:
      matrix:
        component: [backend, frontend]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.component }}
        target: production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to Fly.io Staging
      uses: superfly/flyctl-actions/setup-flyctl@master
    
    - name: Deploy Backend to Staging
      run: |
        flyctl deploy --config backend/fly.staging.toml --image ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:develop
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Frontend to Vercel Staging
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./frontend
        scope: ${{ secrets.VERCEL_ORG_ID }}

    - name: Run smoke tests
      run: |
        # Wait for deployment to be ready
        sleep 30
        
        # Run basic health checks
        curl -f https://api-staging.devhq.com/health
        curl -f https://staging.devhq.com/api/health

    - name: Notify staging deployment failure
      uses: 8398a7/action-slack@v3
      if: failure()
      with:
        status: failure
        text: '🚧 DevHQ Staging Deployment Failed - ${{ github.ref }}'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to Fly.io Production
      uses: superfly/flyctl-actions/setup-flyctl@master
    
    - name: Deploy Backend to Production
      run: |
        flyctl deploy --config backend/fly.toml --image ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:latest
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Frontend to Vercel Production
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        vercel-args: '--prod'
        working-directory: ./frontend
        scope: ${{ secrets.VERCEL_ORG_ID }}

    - name: Run production smoke tests
      run: |
        # Wait for deployment to be ready
        sleep 30
        
        # Run comprehensive health checks
        curl -f https://api.devhq.com/health
        curl -f https://devhq.com/api/health
        
        # Run critical path tests
        npm run test:smoke

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: '🚀 DevHQ Production Deployment Successful!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify production deployment failure
      uses: 8398a7/action-slack@v3
      if: failure()
      with:
        status: failure
        text: '🚨 DevHQ Production Deployment Failed - ${{ github.ref }}'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Performance Testing
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        urls: |
          https://staging.devhq.com
          https://staging.devhq.com/dashboard
          https://staging.devhq.com/projects
        configPath: './frontend/lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true

    - name: Run load tests
      run: |
        # Install k6
        sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6
        
        # Run load tests
        k6 run tests/load/api-load-test.js
        k6 run tests/load/frontend-load-test.js

    - name: Upload performance artifacts
      uses: actions/upload-artifact@v3
      with:
        name: performance-test-results
        path: |
          lighthouse-results/
          k6-results/