# DevHQ Production Environment
# Optimized for production deployment with security and performance

version: '3.8'

services:
  # PostgreSQL Database (Production)
  postgres:
    image: postgres:15-alpine
    container_name: devhq-postgres-prod
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-devhq}
      POSTGRES_USER: ${POSTGRES_USER:-devhq_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: md5
    ports:
      - "127.0.0.1:5432:5432"  # Bind to localhost only
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backups:/backups  # Mount backup directory
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-devhq_user} -d ${POSTGRES_DB:-devhq}"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Production PostgreSQL configuration
    command: >
      postgres
      -c max_connections=100
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200

  # Redis (Production)
  redis:
    image: redis:7-alpine
    container_name: devhq-redis-prod
    restart: always
    ports:
      - "127.0.0.1:6379:6379"  # Bind to localhost only
    volumes:
      - redis_prod_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Production Redis configuration
    command: redis-server /usr/local/etc/redis/redis.conf

  # FastAPI Backend (Production)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: devhq-backend-prod
    restart: always
    environment:
      # Database
      DATABASE_URL: ${DATABASE_URL}
      
      # Redis
      REDIS_URL: ${REDIS_URL:-redis://redis:6379/0}
      
      # Security
      SECRET_KEY: ${SECRET_KEY}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-15}
      REFRESH_TOKEN_EXPIRE_DAYS: ${REFRESH_TOKEN_EXPIRE_DAYS:-7}
      
      # External Services
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      CLOUDINARY_URL: ${CLOUDINARY_URL}
      
      # Email
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      
      # Environment
      ENVIRONMENT: production
      DEBUG: false
      LOG_LEVEL: INFO
      
    ports:
      - "127.0.0.1:8000:8000"  # Bind to localhost only (behind nginx)
    volumes:
      - backend_prod_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Production command with multiple workers
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4 --log-level info
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Next.js Frontend (Production)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: devhq-frontend-prod
    restart: always
    environment:
      # API Configuration
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL}
      NEXT_PUBLIC_API_VERSION: v1
      
      # External Services
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
      NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: ${NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}
      
      # Analytics
      NEXT_PUBLIC_VERCEL_ANALYTICS_ID: ${NEXT_PUBLIC_VERCEL_ANALYTICS_ID}
      NEXT_PUBLIC_SENTRY_DSN: ${NEXT_PUBLIC_SENTRY_DSN}
      
      # Feature Flags
      NEXT_PUBLIC_ENABLE_PAYMENTS: ${NEXT_PUBLIC_ENABLE_PAYMENTS:-true}
      NEXT_PUBLIC_ENABLE_CLIENT_PORTAL: ${NEXT_PUBLIC_ENABLE_CLIENT_PORTAL:-true}
      
      # Environment
      NODE_ENV: production
      
    ports:
      - "127.0.0.1:3000:3000"  # Bind to localhost only (behind nginx)
    depends_on:
      - backend
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Celery Worker (Production)
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: devhq-celery-worker-prod
    restart: always
    environment:
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL:-redis://redis:6379/0}
      SECRET_KEY: ${SECRET_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      CLOUDINARY_URL: ${CLOUDINARY_URL}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      ENVIRONMENT: production
      LOG_LEVEL: INFO
    volumes:
      - backend_prod_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=4
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Celery Beat (Production)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: devhq-celery-beat-prod
    restart: always
    environment:
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL:-redis://redis:6379/0}
      SECRET_KEY: ${SECRET_KEY}
      ENVIRONMENT: production
      LOG_LEVEL: INFO
    volumes:
      - backend_prod_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - devhq-network
    command: celery -A app.tasks.celery_app beat --loglevel=info

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: devhq-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - devhq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_prod_data:
    driver: local
    # IMPORTANT: In production, consider using external volumes or managed databases
    # For Fly.io: Use Fly Postgres or external providers like Neon, Supabase
  redis_prod_data:
    driver: local
  backend_prod_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  devhq-network:
    driver: bridge