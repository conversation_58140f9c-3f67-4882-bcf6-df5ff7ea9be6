# DevHQ Frontend Dockerfile
# Optimized multi-stage build for lean production images

# Base Node.js image with Alpine for smaller size
FROM node:18-alpine as base

# Set environment variables
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=development

# Install dependencies needed for node-gyp and security updates
RUN apk add --no-cache libc6-compat curl && \
    apk upgrade --no-cache

# Set work directory
WORKDIR /app

# Development stage
FROM base as development

# Copy package files for dependency installation
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci --prefer-offline --no-audit

# Copy application code
COPY . .

# Create next user for security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Change ownership
RUN chown -R nextjs:nodejs /app
USER nextjs

# Expose port
EXPOSE 3000

# Development command with hot reloading
CMD ["npm", "run", "dev"]

# Dependencies stage - install only production dependencies
FROM base as deps

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production --prefer-offline --no-audit && \
    npm cache clean --force

# Builder stage - build the application
FROM base as builder

# Copy package files
COPY package*.json ./

# Install all dependencies for building
RUN npm ci --prefer-offline --no-audit

# Copy source code
COPY . .

# Set production environment for build
ENV NODE_ENV=production

# Build the application
RUN npm run build

# Production stage - final lean image
FROM node:18-alpine as production

# Set production environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Install security updates
RUN apk upgrade --no-cache && \
    apk add --no-cache curl

WORKDIR /app

# Create next user for security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy only necessary files from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check for container orchestration
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Production command
CMD ["node", "server.js"]